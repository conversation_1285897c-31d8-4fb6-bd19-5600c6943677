{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-12T10:20:08.007Z", "updatedAt": "2025-08-12T10:20:08.052Z", "resourceCount": 28}, "resources": [{"id": "literary-cinematic-fusion", "source": "project", "protocol": "execution", "name": "Literary Cinematic Fusion 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/神棍/execution/literary-cinematic-fusion.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.038Z", "updatedAt": "2025-08-12T10:20:08.038Z", "scannedAt": "2025-08-12T10:20:08.038Z", "path": "domain/神棍/execution/literary-cinematic-fusion.execution.md"}}, {"id": "novel-writing-mastery", "source": "project", "protocol": "execution", "name": "Novel Writing Mastery 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/神棍/execution/novel-writing-mastery.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.038Z", "updatedAt": "2025-08-12T10:20:08.038Z", "scannedAt": "2025-08-12T10:20:08.038Z", "path": "domain/神棍/execution/novel-writing-mastery.execution.md"}}, {"id": "chinese-fantasy-mastery", "source": "project", "protocol": "knowledge", "name": "Chinese Fantasy Mastery 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/神棍/knowledge/chinese-fantasy-mastery.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.040Z", "updatedAt": "2025-08-12T10:20:08.040Z", "scannedAt": "2025-08-12T10:20:08.040Z", "path": "domain/神棍/knowledge/chinese-fantasy-mastery.knowledge.md"}}, {"id": "narrative-fusion-techniques", "source": "project", "protocol": "knowledge", "name": "Narrative Fusion Techniques 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/神棍/knowledge/narrative-fusion-techniques.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.041Z", "updatedAt": "2025-08-12T10:20:08.041Z", "scannedAt": "2025-08-12T10:20:08.041Z", "path": "domain/神棍/knowledge/narrative-fusion-techniques.knowledge.md"}}, {"id": "novel-master-expertise", "source": "project", "protocol": "knowledge", "name": "Novel Master Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/神棍/knowledge/novel-master-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.042Z", "updatedAt": "2025-08-12T10:20:08.042Z", "scannedAt": "2025-08-12T10:20:08.042Z", "path": "domain/神棍/knowledge/novel-master-expertise.knowledge.md"}}, {"id": "literary-imagination", "source": "project", "protocol": "thought", "name": "Literary Imagination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/神棍/thought/literary-imagination.thought.md", "metadata": {"createdAt": "2025-08-12T10:20:08.043Z", "updatedAt": "2025-08-12T10:20:08.043Z", "scannedAt": "2025-08-12T10:20:08.043Z", "path": "domain/神棍/thought/literary-imagination.thought.md"}}, {"id": "挽棠卿", "source": "project", "protocol": "role", "name": "挽棠卿 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/挽棠卿/挽棠卿.role.md", "metadata": {"createdAt": "2025-08-12T10:20:08.028Z", "updatedAt": "2025-08-12T10:20:08.028Z", "scannedAt": "2025-08-12T10:20:08.028Z", "path": "domain/挽棠卿/挽棠卿.role.md"}}, {"id": "content-quality-control", "source": "project", "protocol": "execution", "name": "Content Quality Control 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/文稿审核/execution/content-quality-control.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.029Z", "updatedAt": "2025-08-12T10:20:08.029Z", "scannedAt": "2025-08-12T10:20:08.029Z", "path": "domain/文稿审核/execution/content-quality-control.execution.md"}}, {"id": "editorial-standards", "source": "project", "protocol": "execution", "name": "Editorial Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/文稿审核/execution/editorial-standards.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.030Z", "updatedAt": "2025-08-12T10:20:08.030Z", "scannedAt": "2025-08-12T10:20:08.030Z", "path": "domain/文稿审核/execution/editorial-standards.execution.md"}}, {"id": "manuscript-review-process", "source": "project", "protocol": "execution", "name": "Manuscript Review Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/文稿审核/execution/manuscript-review-process.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.031Z", "updatedAt": "2025-08-12T10:20:08.031Z", "scannedAt": "2025-08-12T10:20:08.031Z", "path": "domain/文稿审核/execution/manuscript-review-process.execution.md"}}, {"id": "chinese-literature-standards", "source": "project", "protocol": "knowledge", "name": "Chinese Literature Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/文稿审核/knowledge/chinese-literature-standards.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.032Z", "updatedAt": "2025-08-12T10:20:08.032Z", "scannedAt": "2025-08-12T10:20:08.032Z", "path": "domain/文稿审核/knowledge/chinese-literature-standards.knowledge.md"}}, {"id": "fantasy-genre-conventions", "source": "project", "protocol": "knowledge", "name": "Fantasy Genre Conventions 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/文稿审核/knowledge/fantasy-genre-conventions.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.033Z", "updatedAt": "2025-08-12T10:20:08.033Z", "scannedAt": "2025-08-12T10:20:08.033Z", "path": "domain/文稿审核/knowledge/fantasy-genre-conventions.knowledge.md"}}, {"id": "manuscript-review-expertise", "source": "project", "protocol": "knowledge", "name": "Manuscript Review Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/文稿审核/knowledge/manuscript-review-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.033Z", "updatedAt": "2025-08-12T10:20:08.033Z", "scannedAt": "2025-08-12T10:20:08.033Z", "path": "domain/文稿审核/knowledge/manuscript-review-expertise.knowledge.md"}}, {"id": "critical-thinking", "source": "project", "protocol": "thought", "name": "Critical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/文稿审核/thought/critical-thinking.thought.md", "metadata": {"createdAt": "2025-08-12T10:20:08.035Z", "updatedAt": "2025-08-12T10:20:08.035Z", "scannedAt": "2025-08-12T10:20:08.035Z", "path": "domain/文稿审核/thought/critical-thinking.thought.md"}}, {"id": "detail-oriented", "source": "project", "protocol": "thought", "name": "Detail Oriented 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/文稿审核/thought/detail-oriented.thought.md", "metadata": {"createdAt": "2025-08-12T10:20:08.035Z", "updatedAt": "2025-08-12T10:20:08.035Z", "scannedAt": "2025-08-12T10:20:08.035Z", "path": "domain/文稿审核/thought/detail-oriented.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/文稿审核/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-08-12T10:20:08.036Z", "updatedAt": "2025-08-12T10:20:08.036Z", "scannedAt": "2025-08-12T10:20:08.036Z", "path": "domain/文稿审核/thought/quality-control.thought.md"}}, {"id": "文稿审核", "source": "project", "protocol": "role", "name": "文稿审核 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/文稿审核/文稿审核.role.md", "metadata": {"createdAt": "2025-08-12T10:20:08.036Z", "updatedAt": "2025-08-12T10:20:08.036Z", "scannedAt": "2025-08-12T10:20:08.036Z", "path": "domain/文稿审核/文稿审核.role.md"}}, {"id": "神棍", "source": "project", "protocol": "role", "name": "神棍 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/神棍/神棍.role.md", "metadata": {"createdAt": "2025-08-12T10:20:08.044Z", "updatedAt": "2025-08-12T10:20:08.044Z", "scannedAt": "2025-08-12T10:20:08.044Z", "path": "domain/神棍/神棍.role.md"}}, {"id": "content-interpretation", "source": "project", "protocol": "execution", "name": "Content Interpretation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/读者视角/execution/content-interpretation.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.045Z", "updatedAt": "2025-08-12T10:20:08.045Z", "scannedAt": "2025-08-12T10:20:08.045Z", "path": "domain/读者视角/execution/content-interpretation.execution.md"}}, {"id": "emotional-evaluation", "source": "project", "protocol": "execution", "name": "Emotional Evaluation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/读者视角/execution/emotional-evaluation.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.045Z", "updatedAt": "2025-08-12T10:20:08.045Z", "scannedAt": "2025-08-12T10:20:08.045Z", "path": "domain/读者视角/execution/emotional-evaluation.execution.md"}}, {"id": "reader-experience-analysis", "source": "project", "protocol": "execution", "name": "Reader Experience Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/读者视角/execution/reader-experience-analysis.execution.md", "metadata": {"createdAt": "2025-08-12T10:20:08.046Z", "updatedAt": "2025-08-12T10:20:08.046Z", "scannedAt": "2025-08-12T10:20:08.046Z", "path": "domain/读者视角/execution/reader-experience-analysis.execution.md"}}, {"id": "audience-analysis", "source": "project", "protocol": "knowledge", "name": "Audience Analysis 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/读者视角/knowledge/audience-analysis.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.047Z", "updatedAt": "2025-08-12T10:20:08.047Z", "scannedAt": "2025-08-12T10:20:08.047Z", "path": "domain/读者视角/knowledge/audience-analysis.knowledge.md"}}, {"id": "reader-perspective-expertise", "source": "project", "protocol": "knowledge", "name": "Reader Perspective Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/读者视角/knowledge/reader-perspective-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.048Z", "updatedAt": "2025-08-12T10:20:08.048Z", "scannedAt": "2025-08-12T10:20:08.048Z", "path": "domain/读者视角/knowledge/reader-perspective-expertise.knowledge.md"}}, {"id": "reading-psychology", "source": "project", "protocol": "knowledge", "name": "Reading Psychology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/读者视角/knowledge/reading-psychology.knowledge.md", "metadata": {"createdAt": "2025-08-12T10:20:08.049Z", "updatedAt": "2025-08-12T10:20:08.049Z", "scannedAt": "2025-08-12T10:20:08.049Z", "path": "domain/读者视角/knowledge/reading-psychology.knowledge.md"}}, {"id": "emotional-resonance", "source": "project", "protocol": "thought", "name": "Emotional Resonance 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/读者视角/thought/emotional-resonance.thought.md", "metadata": {"createdAt": "2025-08-12T10:20:08.049Z", "updatedAt": "2025-08-12T10:20:08.049Z", "scannedAt": "2025-08-12T10:20:08.049Z", "path": "domain/读者视角/thought/emotional-resonance.thought.md"}}, {"id": "empathetic-reading", "source": "project", "protocol": "thought", "name": "Empathetic Reading 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/读者视角/thought/empathetic-reading.thought.md", "metadata": {"createdAt": "2025-08-12T10:20:08.050Z", "updatedAt": "2025-08-12T10:20:08.050Z", "scannedAt": "2025-08-12T10:20:08.050Z", "path": "domain/读者视角/thought/empathetic-reading.thought.md"}}, {"id": "reader-psychology", "source": "project", "protocol": "thought", "name": "Reader Psychology 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/读者视角/thought/reader-psychology.thought.md", "metadata": {"createdAt": "2025-08-12T10:20:08.051Z", "updatedAt": "2025-08-12T10:20:08.051Z", "scannedAt": "2025-08-12T10:20:08.051Z", "path": "domain/读者视角/thought/reader-psychology.thought.md"}}, {"id": "读者视角", "source": "project", "protocol": "role", "name": "读者视角 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/读者视角/读者视角.role.md", "metadata": {"createdAt": "2025-08-12T10:20:08.051Z", "updatedAt": "2025-08-12T10:20:08.051Z", "scannedAt": "2025-08-12T10:20:08.051Z", "path": "domain/读者视角/读者视角.role.md"}}], "stats": {"totalResources": 28, "byProtocol": {"execution": 8, "knowledge": 9, "thought": 7, "role": 4}, "bySource": {"project": 28}}}